package handler

import (
	"net/http"

	"yekaitai/internal/modules/medlinker_sync/service"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/response"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// SyncHandler 医联数据同步处理器
type SyncHandler struct {
	syncService *service.SyncService
}

// NewSyncHandler 创建同步处理器
func NewSyncHandler(medlinkerClient *medlinker.MedlinkerClient) *SyncHandler {
	return &SyncHandler{
		syncService: service.NewSyncService(medlinkerClient),
	}
}

// SyncAllDataRequest 全量同步请求
type SyncAllDataRequest struct {
	Force bool `json:"force,optional"` // 是否强制同步
}

// SyncAllDataResponse 全量同步响应
type SyncAllDataResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
}

// SyncAllData 全量同步数据到医联
func (h *SyncHandler) SyncAllData(w http.ResponseWriter, r *http.Request) {
	var req SyncAllDataRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		response.Error(w, http.StatusBadRequest, "请求参数错误")
		return
	}

	logx.Infof("开始全量同步数据到医联, force=%v", req.Force)

	err := h.syncService.SyncAllData()
	if err != nil {
		logx.Errorf("全量同步数据失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "同步失败: "+err.Error())
		return
	}

	resp := SyncAllDataResponse{
		Success: true,
		Message: "全量同步数据成功",
	}

	response.Success(w, resp)
	logx.Info("全量同步数据到医联完成")
}

// SyncIncrementalDataResponse 增量同步响应
type SyncIncrementalDataResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
}

// SyncIncrementalData 增量同步数据到医联
func (h *SyncHandler) SyncIncrementalData(w http.ResponseWriter, r *http.Request) {
	logx.Info("开始增量同步数据到医联")

	err := h.syncService.SyncIncrementalData()
	if err != nil {
		logx.Errorf("增量同步数据失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "同步失败: "+err.Error())
		return
	}

	resp := SyncIncrementalDataResponse{
		Success: true,
		Message: "增量同步数据成功",
	}

	response.Success(w, resp)
	logx.Info("增量同步数据到医联完成")
}

// TestMedlinkerConnectionRequest 测试医联连接请求
type TestMedlinkerConnectionRequest struct {
	Phone string `json:"phone,optional"` // 测试电话号码
}

// TestMedlinkerConnectionResponse 测试医联连接响应
type TestMedlinkerConnectionResponse struct {
	Success   bool   `json:"success"`    // 是否成功
	Message   string `json:"message"`    // 消息
	Token     string `json:"token"`      // 获取到的token（脱敏）
	BaseURL   string `json:"base_url"`   // 医联API地址
	ModelID   int    `json:"model_id"`   // 模型ID
	CallCount int    `json:"call_count"` // 当日调用次数
	CallLimit int    `json:"call_limit"` // 调用次数限制
}

// TestMedlinkerConnection 测试医联连接
func (h *SyncHandler) TestMedlinkerConnection(w http.ResponseWriter, r *http.Request) {
	var req TestMedlinkerConnectionRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		response.Error(w, http.StatusBadRequest, "请求参数错误")
		return
	}

	phone := req.Phone
	if phone == "" {
		phone = "18888888888" // 默认测试电话
	}

	logx.Infof("测试医联连接, phone=%s", phone)

	// 获取医联客户端配置
	config := h.syncService.GetMedlinkerConfig()

	// 创建临时客户端进行测试
	testClient := medlinker.NewMedlinkerClient(config)

	// 尝试登录
	err := testClient.Login(phone)
	if err != nil {
		logx.Errorf("医联登录测试失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "医联连接失败: "+err.Error())
		return
	}

	// 获取token（脱敏显示）
	token := testClient.GetToken()
	maskedToken := ""
	if len(token) > 10 {
		maskedToken = token[:10] + "..."
	} else {
		maskedToken = token
	}

	// 获取调用次数信息
	callCount, callLimit, _ := testClient.GetDailyCallInfo()

	resp := TestMedlinkerConnectionResponse{
		Success:   true,
		Message:   "医联连接测试成功",
		Token:     maskedToken,
		BaseURL:   config.BaseURL,
		ModelID:   config.ModelID,
		CallCount: callCount,
		CallLimit: callLimit,
	}

	response.Success(w, resp)
	logx.Info("医联连接测试成功")
}

// GetSyncStatusResponse 获取同步状态响应
type GetSyncStatusResponse struct {
	LastFullSync        string `json:"last_full_sync"`        // 最后一次全量同步时间
	LastIncrementalSync string `json:"last_incremental_sync"` // 最后一次增量同步时间
	TotalStores         int    `json:"total_stores"`          // 总门店数
	TotalDepartments    int    `json:"total_departments"`     // 总科室数
	TotalDoctors        int    `json:"total_doctors"`         // 总医生数
	SyncEnabled         bool   `json:"sync_enabled"`          // 是否启用同步
}

// GetSyncStatus 获取同步状态
func (h *SyncHandler) GetSyncStatus(w http.ResponseWriter, r *http.Request) {
	logx.Info("获取医联同步状态")

	status, err := h.syncService.GetSyncStatus()
	if err != nil {
		logx.Errorf("获取同步状态失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "获取状态失败: "+err.Error())
		return
	}

	response.Success(w, status)
	logx.Info("获取医联同步状态成功")
}

// SetSyncConfigRequest 设置同步配置请求
type SetSyncConfigRequest struct {
	Enabled        bool   `json:"enabled"`          // 是否启用同步
	SyncInterval   int    `json:"sync_interval"`    // 同步间隔（分钟）
	Phone          string `json:"phone"`            // 医联认证电话
	BaseURL        string `json:"base_url"`         // 医联API地址
	ModelID        int    `json:"model_id"`         // 模型ID
	DailyCallLimit int    `json:"daily_call_limit"` // 每日调用限制
}

// SetSyncConfigResponse 设置同步配置响应
type SetSyncConfigResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
}

// SetSyncConfig 设置同步配置
func (h *SyncHandler) SetSyncConfig(w http.ResponseWriter, r *http.Request) {
	var req SetSyncConfigRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		response.Error(w, http.StatusBadRequest, "请求参数错误")
		return
	}

	logx.Infof("设置医联同步配置: enabled=%v, interval=%d", req.Enabled, req.SyncInterval)

	err := h.syncService.SetSyncConfig(req.Enabled, req.SyncInterval, req.Phone, req.BaseURL, req.ModelID, req.DailyCallLimit)
	if err != nil {
		logx.Errorf("设置同步配置失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "设置配置失败: "+err.Error())
		return
	}

	resp := SetSyncConfigResponse{
		Success: true,
		Message: "同步配置设置成功",
	}

	response.Success(w, resp)
	logx.Info("医联同步配置设置成功")
}

// GetSyncLogsRequest 获取同步日志请求
type GetSyncLogsRequest struct {
	Page     int    `form:"page,default=1"`       // 页码
	PageSize int    `form:"page_size,default=20"` // 每页大小
	Type     string `form:"type,optional"`        // 同步类型：full/incremental
	Status   string `form:"status,optional"`      // 状态：success/failed
}

// GetSyncLogsResponse 获取同步日志响应
type GetSyncLogsResponse struct {
	List     []SyncLogItem `json:"list"`      // 日志列表
	Total    int64         `json:"total"`     // 总数
	Page     int           `json:"page"`      // 当前页
	PageSize int           `json:"page_size"` // 每页大小
}

// SyncLogItem 同步日志项
type SyncLogItem struct {
	ID        uint   `json:"id"`         // 日志ID
	Type      string `json:"type"`       // 同步类型
	Status    string `json:"status"`     // 状态
	Message   string `json:"message"`    // 消息
	StartTime string `json:"start_time"` // 开始时间
	EndTime   string `json:"end_time"`   // 结束时间
	Duration  int    `json:"duration"`   // 耗时（秒）
}

// GetSyncLogs 获取同步日志
func (h *SyncHandler) GetSyncLogs(w http.ResponseWriter, r *http.Request) {
	var req GetSyncLogsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		response.Error(w, http.StatusBadRequest, "请求参数错误")
		return
	}

	logx.Infof("获取医联同步日志: page=%d, pageSize=%d, type=%s, status=%s",
		req.Page, req.PageSize, req.Type, req.Status)

	logs, total, err := h.syncService.GetSyncLogs(req.Page, req.PageSize, req.Type, req.Status)
	if err != nil {
		logx.Errorf("获取同步日志失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "获取日志失败: "+err.Error())
		return
	}

	resp := GetSyncLogsResponse{
		List:     logs,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	response.Success(w, resp)
	logx.Info("获取医联同步日志成功")
}
