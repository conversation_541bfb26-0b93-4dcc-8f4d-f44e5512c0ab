package router

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"yekaitai/internal/middleware"

	"yekaitai/internal/router/routes"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// RegisterHandlers 注册API路由
func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	// 测试日志 - 检查日志系统是否正常
	logx.Info("=== 路由注册开始 - 这是一条测试日志 ===")

	// 设置统一的错误处理器
	types.SetupErrorHandler()

	// 全局中间件 - 移除CORS和OPTIONS相关中间件，使用Go-Zero原生CORS
	server.Use(middleware.LogMiddleware())
	logx.Info("=== 全局中间件注册完成 ===")

	// 创建带基础路径前缀的路由组
	apiGroup := &routeGroup{
		server: server,
		prefix: "/yekaitai-admin-api",
	}

	// 注册通用路由
	routes.RegisterCommonRoutes(apiGroup, serverCtx) // 公共路由
	routes.RegisterAuthRoutes(apiGroup, serverCtx)   // 认证路由

	// 注册不需要认证的回调路由
	// routes.RegisterWanLiNiuCallbackRoutes(server, serverCtx) // 万里牛回调路由 - 无需认证

	// 注册需要管理员认证的路由（包含操作日志记录）
	registerWithAdminAuth(apiGroup, serverCtx, func(customServer routes.RestServer, ctx *svc.ServiceContext) {
		// 权限管理路由
		routes.RegisterRBACRoutes(customServer, ctx)    // RBAC相关路由
		routes.RegisterContentRoutes(customServer, ctx) // 内容管理路由
		routes.RegisterPatientRoutes(customServer, ctx) // 患者管理路由

		// 基础管理路由
		routes.RegisterAdminRoutes(customServer, ctx)                   // 管理员用户管理路由
		routes.RegisterStoreRoutes(customServer, ctx)                   // 门店管理路由
		routes.RegisterRegionRoutes(customServer, ctx)                  // 地区管理路由
		routes.RegisterAreaRoutes(customServer, ctx)                    // 区域管理路由
		routes.RegisterServicePackageRoutes(customServer, ctx)          // 服务套餐管理路由
		routes.RegisterServicePackageRecommendRoutes(customServer, ctx) // 推荐服务套餐管理路由

		routes.RegisterUploadRoutes(customServer, ctx)          // 文件上传路由
		routes.RegisterAIDataUploadRoutes(customServer, ctx)    // AI知识库上传路由
		routes.RegisterMemberRoutes(customServer, ctx)          // 会员管理路由
		routes.RegisterDoctorRoutes(customServer, ctx)          // 医生管理路由
		routes.RegisterDoctorRecommendRoutes(customServer, ctx) // 医生推荐管理路由
		routes.RegisterAbcYunRoutes(customServer, ctx)          // ABC云基础接口路由
		routes.RegisterTagRoutes(customServer, ctx)             // 标签管理路由

		// 商城管理路由
		routes.RegisterGoodsRoutes(customServer, ctx)  // 商城管理路由
		routes.RegisterCouponRoutes(customServer, ctx) // 优惠券管理路由

		// 服务评价管理路由
		routes.RegisterEvaluationRoutes(customServer, ctx) // 服务评价管理路由

		// 操作日志路由
		routes.RegisterAdminOperationLogRoutes(customServer) // 操作日志管理路由

		// 用户等级管理路由
		routes.UserLevelRoutes(customServer, ctx) // 用户等级管理路由
		// 叶小币管理路由
		routes.UserPointsRoutes(customServer, ctx) // 叶小币管理路由
		// 兑换管理路由
		routes.RegisterCoinExchangeRoutes(customServer, ctx) // 积分兑换管理路由
		// 短信设置路由
		routes.RegisterSmsSetupRoutes(customServer, ctx) // 短信设置管理路由
		// 核销员管理路由
		routes.RegisterWxRedeemerRoutes(customServer, ctx) // 核销员管理路由

		// 医联数据同步路由
		routes.RegisterMedlinkerSyncRoutes(customServer, ctx) // 医联数据同步管理路由
		routes.RegisterMedlinkerTaskRoutes(customServer, ctx) // 医联任务管理路由
	})

	logx.Info("=== 所有路由注册完成 - 这是一条测试日志 ===")
}

// registerWithAdminAuth 注册需要管理员认证的路由
func registerWithAdminAuth(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext, registerFunc func(routes.RestServer, *svc.ServiceContext)) {
	// 创建中间件管理器
	logx.Info("=== 创建中间件管理器 ===")
	middlewareManager := middleware.NewMiddlewareManager()
	logx.Info("=== 中间件管理器创建完成 ===")

	// 创建一个自定义服务器，拦截所有路由注册
	adminAuthServer := &adminAuthServer{
		server:            server,
		serverCtx:         serverCtx,
		middlewareManager: middlewareManager,
	}

	// 调用注册函数，但路由会被拦截
	registerFunc(adminAuthServer, serverCtx)
}

// HealthCheck 健康检查处理器
func HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

// adminAuthServer 管理员认证服务器，用于拦截路由注册
type adminAuthServer struct {
	server interface {
		AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
		AddRoute(r rest.Route, opts ...rest.RouteOption)
		Use(middleware rest.Middleware)
	}
	serverCtx         *svc.ServiceContext
	middlewareManager *middleware.MiddlewareManager
}

// 实现AddRoutes方法，为每个路由添加管理员认证中间件
func (s *adminAuthServer) AddRoutes(rs []rest.Route, opts ...rest.RouteOption) {
	// 创建带管理员认证的路由
	wrappedRoutes := make([]rest.Route, len(rs))
	for i, r := range rs {
		route := r
		originalHandler := route.Handler

		// 包装处理函数，添加管理员认证和操作日志中间件
		route.Handler = func(w http.ResponseWriter, r *http.Request) {
			// 测试日志 - 检查认证中间件是否被调用
			logx.Infof("=== 认证中间件开始处理请求: %s %s ===", r.Method, r.URL.Path)

			// 从请求头获取Token
			authHeader := r.Header.Get("Authorization")
			logx.Infof("=== 获取到Authorization头: %s ===", authHeader)
			if authHeader == "" {
				logx.Error("=== 缺少Authorization头 ===")
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请提供认证信息"))
				return
			}

			// 记录认证请求
			logx.Infof("认证请求: %s %s", r.Method, r.URL.Path)

			// 解析Token
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
				return
			}

			tokenType := strings.ToLower(parts[0])
			tokenString := parts[1]

			// 验证认证类型
			if tokenType != "bearer" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型"))
				return
			}

			// 尝试管理员Token解析
			adminClaims, adminErr := middleware.ParseAdminToken(s.serverCtx, tokenString)
			if adminErr != nil {
				// Token解析失败
				logx.Errorf("Token解析失败: %v", adminErr)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(
					types.CodeUnauthorized,
					fmt.Sprintf("认证失败: %v", adminErr),
				))
				return
			}

			// 管理员Token有效
			ctx := r.Context()
			// 设置用户信息到上下文
			ctx = context.WithValue(ctx, types.UserIDKey, adminClaims.UserID)
			ctx = context.WithValue(ctx, "admin_id", strconv.FormatUint(uint64(adminClaims.UserID), 10))
			ctx = context.WithValue(ctx, types.RoleKey, strings.Join(adminClaims.Roles, ","))
			if len(adminClaims.Roles) > 0 {
				ctx = context.WithValue(ctx, "admin_roles", strings.Join(adminClaims.Roles, ","))
			}
			if len(adminClaims.Perms) > 0 {
				ctx = context.WithValue(ctx, "admin_perms", strings.Join(adminClaims.Perms, ","))
			}
			logx.Infof("认证成功: 管理员ID=%d", adminClaims.UserID)

			// 设置管理员ID到请求头，供操作日志中间件使用
			r.Header.Set("X-Admin-ID", strconv.FormatUint(uint64(adminClaims.UserID), 10))

			// 应用操作日志中间件，然后调用原始处理函数
			logx.Infof("认证中间件: 准备应用操作日志中间件")
			wrappedHandler := s.middlewareManager.WrapAdminHandler(originalHandler)
			logx.Infof("认证中间件: 操作日志中间件已包装，准备调用处理函数")
			wrappedHandler(w, r.WithContext(ctx))
		}

		wrappedRoutes[i] = route
	}

	// 将包装后的路由添加到服务器
	s.server.AddRoutes(wrappedRoutes, opts...)
}

// AddRoute 实现 RestServer 接口，添加单个路由
func (s *adminAuthServer) AddRoute(r rest.Route, opts ...rest.RouteOption) {
	// 包装处理函数，添加管理员认证
	originalHandler := r.Handler
	r.Handler = func(w http.ResponseWriter, req *http.Request) {
		// 从请求头获取Token
		authHeader := req.Header.Get("Authorization")
		if authHeader == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请提供认证信息"))
			return
		}

		// 记录认证请求
		logx.Infof("认证请求: %s %s", req.Method, req.URL.Path)

		// 解析Token
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
			return
		}

		tokenType := strings.ToLower(parts[0])
		tokenString := parts[1]

		// 验证认证类型
		if tokenType != "bearer" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型"))
			return
		}

		// 尝试管理员Token解析
		adminClaims, adminErr := middleware.ParseAdminToken(s.serverCtx, tokenString)
		if adminErr != nil {
			// Token解析失败
			logx.Errorf("Token解析失败: %v", adminErr)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(
				types.CodeUnauthorized,
				fmt.Sprintf("认证失败: %v", adminErr),
			))
			return
		}

		// 管理员Token有效
		ctx := req.Context()
		// 设置用户信息到上下文
		ctx = context.WithValue(ctx, types.UserIDKey, adminClaims.UserID)
		ctx = context.WithValue(ctx, "admin_id", strconv.FormatUint(uint64(adminClaims.UserID), 10))
		ctx = context.WithValue(ctx, types.RoleKey, strings.Join(adminClaims.Roles, ","))
		if len(adminClaims.Roles) > 0 {
			ctx = context.WithValue(ctx, "admin_roles", strings.Join(adminClaims.Roles, ","))
		}
		if len(adminClaims.Perms) > 0 {
			ctx = context.WithValue(ctx, "admin_perms", strings.Join(adminClaims.Perms, ","))
		}
		logx.Infof("认证成功: 管理员ID=%d", adminClaims.UserID)

		// 设置管理员ID到请求头，供操作日志中间件使用
		req.Header.Set("X-Admin-ID", strconv.FormatUint(uint64(adminClaims.UserID), 10))

		// 应用操作日志中间件，然后调用原始处理函数
		wrappedHandler := s.middlewareManager.WrapAdminHandler(originalHandler)
		wrappedHandler(w, req.WithContext(ctx))
	}

	// 将包装后的路由添加到服务器
	s.server.AddRoute(r, opts...)
}

// 实现必要的其他方法
func (s *adminAuthServer) Use(middleware rest.Middleware) {
	s.server.Use(middleware)
}

// routeGroup 路由组，支持路径前缀
type routeGroup struct {
	server *rest.Server
	prefix string
}

// AddRoutes 实现路由注册，添加路径前缀
func (rg *routeGroup) AddRoutes(rs []rest.Route, opts ...rest.RouteOption) {
	// 为每个路由添加前缀
	prefixedRoutes := make([]rest.Route, len(rs))
	for i, r := range rs {
		route := r
		route.Path = rg.prefix + route.Path
		prefixedRoutes[i] = route
	}
	rg.server.AddRoutes(prefixedRoutes, opts...)
}

// AddRoute 添加单个路由，添加路径前缀
func (rg *routeGroup) AddRoute(r rest.Route, opts ...rest.RouteOption) {
	r.Path = rg.prefix + r.Path
	rg.server.AddRoute(r, opts...)
}

// Use 添加中间件
func (rg *routeGroup) Use(middleware rest.Middleware) {
	rg.server.Use(middleware)
}
