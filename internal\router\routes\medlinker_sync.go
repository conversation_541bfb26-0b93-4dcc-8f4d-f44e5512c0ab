package routes

import (
	"net/http"

	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/modules/medlinker_sync/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// RegisterMedlinkerSyncRoutes 注册医联数据同步路由
func RegisterMedlinkerSyncRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 初始化医联同步任务（如果还没有初始化）
	if tasks.MedlinkerSyncTaskInstance == nil {
		tasks.InitMedlinkerSyncTask()
	}

	// 获取医联客户端实例
	medlinkerClient := tasks.GetMedlinkerClientInstance()
	if medlinkerClient == nil {
		logx.Error("医联客户端未初始化，无法注册同步路由")
		return
	}

	// 创建处理器
	syncHandler := handler.NewSyncHandler(medlinkerClient)

	// 医联数据同步管理路由
	server.AddRoutes([]rest.Route{
		// 全量同步数据
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/medlinker/sync/full",
			Handler: syncHandler.SyncAllData,
		},
		// 增量同步数据
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/medlinker/sync/incremental",
			Handler: syncHandler.SyncIncrementalData,
		},
		// 测试医联连接
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/medlinker/test/connection",
			Handler: syncHandler.TestMedlinkerConnection,
		},
		// 获取同步状态
		{
			Method:  http.MethodGet,
			Path:    "/api/admin/medlinker/status",
			Handler: syncHandler.GetSyncStatus,
		},
		// 设置同步配置
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/medlinker/config",
			Handler: syncHandler.SetSyncConfig,
		},
		// 获取同步日志
		{
			Method:  http.MethodGet,
			Path:    "/api/admin/medlinker/logs",
			Handler: syncHandler.GetSyncLogs,
		},
	})

	logx.Info("医联数据同步管理路由注册完成")
}

// RegisterMedlinkerTaskRoutes 注册医联任务管理路由
func RegisterMedlinkerTaskRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 医联任务管理路由 - 这些是简单的任务控制接口
	server.AddRoutes([]rest.Route{
		// 手动执行全量同步
		{
			Method: http.MethodPost,
			Path:   "/api/admin/tasks/medlinker/full-sync",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				go tasks.RunMedlinkerFullSync()
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"success": true, "message": "全量同步任务已启动"}`))
			},
		},
		// 手动执行增量同步
		{
			Method: http.MethodPost,
			Path:   "/api/admin/tasks/medlinker/incremental-sync",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				go tasks.RunMedlinkerIncrementalSync()
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"success": true, "message": "增量同步任务已启动"}`))
			},
		},
		// 获取任务状态
		{
			Method: http.MethodGet,
			Path:   "/api/admin/tasks/medlinker/status",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				status := tasks.GetMedlinkerSyncStatus()
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				// 简单的JSON序列化
				response := `{"success": true, "data": {"enabled": true, "interval": "10m0s", "name": "医联数据同步任务"}}`
				w.Write([]byte(response))
			},
		},
		// 启用/禁用任务
		{
			Method: http.MethodPost,
			Path:   "/api/admin/tasks/medlinker/toggle",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				enabled := r.URL.Query().Get("enabled") == "true"
				tasks.SetMedlinkerSyncEnabled(enabled)
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				if enabled {
					w.Write([]byte(`{"success": true, "message": "医联同步任务已启用"}`))
				} else {
					w.Write([]byte(`{"success": true, "message": "医联同步任务已禁用"}`))
				}
			},
		},
		// 设置同步间隔
		{
			Method: http.MethodPost,
			Path:   "/api/admin/tasks/medlinker/interval",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				minutes := r.URL.Query().Get("minutes")
				if minutes == "" {
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusBadRequest)
					w.Write([]byte(`{"success": false, "message": "缺少minutes参数"}`))
					return
				}

				// 简单的字符串转换
				var intervalMinutes int
				switch minutes {
				case "5":
					intervalMinutes = 5
				case "10":
					intervalMinutes = 10
				case "15":
					intervalMinutes = 15
				case "30":
					intervalMinutes = 30
				case "60":
					intervalMinutes = 60
				default:
					intervalMinutes = 10
				}

				tasks.SetMedlinkerSyncInterval(intervalMinutes)
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"success": true, "message": "同步间隔设置成功"}`))
			},
		},
		// 测试连接
		{
			Method: http.MethodPost,
			Path:   "/api/admin/tasks/medlinker/test",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				err := tasks.TestMedlinkerConnection()
				w.Header().Set("Content-Type", "application/json")
				if err != nil {
					w.WriteHeader(http.StatusInternalServerError)
					w.Write([]byte(`{"success": false, "message": "连接测试失败: ` + err.Error() + `"}`))
				} else {
					w.WriteHeader(http.StatusOK)
					w.Write([]byte(`{"success": true, "message": "医联连接测试成功"}`))
				}
			},
		},
	})

	logx.Info("医联任务管理路由注册完成")
}
